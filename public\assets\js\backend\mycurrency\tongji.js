define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    // 检查腾讯地图API是否完全加载
    var isTencentMapReady = function() {
        return window.TMap &&
               window.TMap.Map &&
               window.TMap.MultiMarker &&
               window.TMap.InfoWindow &&
               window.TMap.LatLng &&
               window.TMap.LatLngBounds &&
               window.TMap.MarkerStyle;
    };

    // 动态加载腾讯地图API
    var loadTencentMapAPI = function(callback) {
        console.log('开始加载腾讯地图API...');

        if (isTencentMapReady()) {
            console.log('腾讯地图API已经加载完成');
            callback && callback();
            return;
        }

        // 设置全局回调函数
        window.initTencentMapCallback = function() {
            console.log('腾讯地图API回调触发');
            // 等待一小段时间确保API完全初始化
            setTimeout(function() {
                if (isTencentMapReady()) {
                    console.log('腾讯地图API验证成功');
                    callback && callback();
                } else {
                    console.error('腾讯地图API验证失败');
                    $('#map-loading').hide();
                    $('#store-map').html('<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: rgba(255,255,255,0.7); text-align: center;"><div><i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 10px;"></i><br>地图API验证失败<br><small>请刷新页面重试</small></div></div>');
                }
            }, 100);
        };

        // 创建script标签加载腾讯地图API
        var script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = 'https://map.qq.com/api/gljs?v=1.exp&key=QL3BZ-X2IHU-PUIVR-GTZUG-HJWFS-XYB2U&callback=initTencentMapCallback';

        script.onerror = function() {
            console.error('腾讯地图API脚本加载失败');
            $('#map-loading').hide();
            $('#store-map').html('<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: rgba(255,255,255,0.7); text-align: center;"><div><i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 10px;"></i><br>地图加载失败<br><small>请检查网络连接</small></div></div>');
        };

        // 设置超时处理
        setTimeout(function() {
            if (!isTencentMapReady()) {
                console.error('腾讯地图API加载超时');
                $('#map-loading').hide();
                $('#store-map').html('<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: rgba(255,255,255,0.7); text-align: center;"><div><i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 10px;"></i><br>地图加载超时<br><small>请检查网络连接</small></div></div>');
            }
        }, 10000); // 10秒超时

        document.head.appendChild(script);
        console.log('腾讯地图API脚本已添加到页面');
    };

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'mycurrency/tongji/index' + location.search,
                    add_url: 'mycurrency/tongji/add',
                    edit_url: 'mycurrency/tongji/edit',
                    del_url: 'mycurrency/tongji/del',
                    multi_url: 'mycurrency/tongji/multi',
                    import_url: 'mycurrency/tongji/import',
                    table: 'test',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'weigh',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'user_id', title: __('User_id')},
                        {field: 'title', title: __('Title'), operate: 'LIKE'},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        recyclebin: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    'dragsort_url': ''
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: 'mycurrency/tongji/recyclebin' + location.search,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'title', title: __('Title'), align: 'left'},
                        {
                            field: 'deletetime',
                            title: __('Deletetime'),
                            operate: 'RANGE',
                            addclass: 'datetimerange',
                            formatter: Table.api.formatter.datetime
                        },
                        {
                            field: 'operate',
                            width: '140px',
                            title: __('Operate'),
                            table: table,
                            events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'Restore',
                                    text: __('Restore'),
                                    classname: 'btn btn-xs btn-info btn-ajax btn-restoreit',
                                    icon: 'fa fa-rotate-left',
                                    url: 'mycurrency/tongji/restore',
                                    refresh: true
                                },
                                {
                                    name: 'Destroy',
                                    text: __('Destroy'),
                                    classname: 'btn btn-xs btn-danger btn-ajax btn-destroyit',
                                    icon: 'fa fa-times',
                                    url: 'mycurrency/tongji/destroy',
                                    refresh: true
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },

        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        index1: function () {
             // 初始化页面
            Controller.api.bindevent();

            // 显示加载状态
            $('.loading-overlay').addClass('active');

            // 初始化所有图表
            Controller.api.initCharts();

            // 初始化日期选择器
            Controller.api.initDatePicker();

            // 默认激活"日"按钮
            $('.filter-btn[data-type="day"]').addClass('active');

            // 加载营业收入统计数据
            Controller.api.loadRevenueData();

            // 绑定日期选择器事件
            $(document).on('click', '.date-placeholder', function () {
                // 这里由 initDatePicker 处理
            });

            // 绑定筛选按钮点击事件
            $(document).on('click', '.filter-btn', function () {
                $('.filter-btn').removeClass('active');
                $(this).addClass('active');

                // 显示加载状态
                $('.loading-overlay').addClass('active');

                // 重新加载数据，根据选择的时间筛选
                Controller.api.loadRevenueData();
            });

            // 绑定搜索按钮点击事件
            $(document).on('click', '.search-icon-btn', function () {
                // 显示加载状态
                $('.loading-overlay').addClass('active');

                // 重新加载数据，根据选择的日期范围筛选
                Controller.api.loadRevenueData();
            });

            // 调整窗口大小时重绘图表
            $(window).on('resize', function() {
                Controller.api.resizeCharts();
            });
        },


    
        index2: function () {
            // 初始化页面
            Controller.api.bindevent();

            // 初始化AOS动画库
            Controller.api.initAOS();

            // 初始化页面UI
            Controller.api.initPageUI();

            // 初始化筛选功能
            Controller.api.initFilter();

            // 加载柜子状态统计数据
            Controller.api.loadCabinetStats();
            
            // 加载平台统计数据
            Controller.api.loadPlatformStats();

            // 初始化时间筛选功能
            Controller.api.initTimeFilter();

            // 初始化排行榜功能
            Controller.api.initRanking();

            // 延迟初始化图表，确保容器已经正确显示
            setTimeout(function() {
                Controller.api.initCharts();
                // 加载图表数据（默认不筛选时间）
                Controller.api.loadChartData();

                // 动态加载腾讯地图API并初始化地图
                console.log('准备加载腾讯地图API...');
                loadTencentMapAPI(function() {
                    console.log('腾讯地图API加载完成，开始初始化地图...');
                    setTimeout(function() {
                        Controller.api.initMap();
                        setTimeout(function() {
                            Controller.api.loadMapData();
                        }, 500);
                    }, 200);
                });
            }, 1000);

            // 确保下拉框样式正确
            setTimeout(function() {
                // 再次应用下拉框样式
                $('.ranking-select').css({
                    'color': '#ffffff',
                    'background-color': 'rgba(255, 255, 255, 0.1)',
                    'border': '1px solid rgba(255, 255, 255, 0.2)',
                    'border-radius': '10px',
                    'padding': '8px 12px',
                    'padding-right': '30px',
                    'appearance': 'auto',
                    'min-width': '120px'
                });
                
                $('.ranking-select option').css({
                    'background': '#1a2332',
                    'color': '#ffffff',
                    'padding': '8px'
                });
            }, 2000);

            // 绑定刷新按钮事件
            $('#refresh-cabinet-stats').on('click', function() {
                Controller.api.refreshCabinetStats();
                Controller.api.loadChartData();
                Controller.api.loadCurrentRankingData(); // 刷新当前排行榜数据
                // 只有在地图已初始化的情况下才刷新地图数据
                if (Controller.api.map && Controller.api.markerLayer) {
                    Controller.api.loadMapData();
                }
            });
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            },
            // 初始化日期选择器 (index1调用)
            initDatePicker: function() {
                // 注意：这里假设页面已经包含了laydate插件
                // 如果没有，需要先加载
                if (typeof laydate !== 'undefined') {
                    // 开始日期
                    laydate.render({
                        elem: '.date-placeholder:eq(0)',
                        type: 'datetime',
                        trigger: 'click',
                        done: function(value, date) {
                            $('.date-placeholder:eq(0)').text(value);
                        }
                    });

                    // 结束日期
                    laydate.render({
                        elem: '.date-placeholder:eq(1)',
                        type: 'datetime',
                        trigger: 'click',
                        done: function(value, date) {
                            $('.date-placeholder:eq(1)').text(value);
                        }
                    });
                } else {
                    console.warn('laydate not found, date picker not initialized');
                    // 降级方案：添加简单的点击处理
                    $('.date-placeholder').on('click', function() {
                        Toastr.info('日期选择器尚未完全初始化，请直接点击筛选按钮使用预设时间范围');
                    });
                }
            },

            // 初始化所有图表(index1调用)
            initCharts: function() {
                // 初始化迷你图表
                for (let i = 1; i <= 7; i++) {
                    Controller.api.initMiniChart('miniChart' + i);
                }

                // 初始化主要图表
                Controller.api.initRevenueChart();
                Controller.api.initOrdersChart();
            },

            // 调整图表大小 (index1调用)
            resizeCharts: function() {
                // 调整迷你图表
                for (let i = 1; i <= 7; i++) {
                    if (window['miniChart' + i]) {
                        window['miniChart' + i].resize();
                    }
                }

                // 调整主要图表
                if (window.revenueChart) {
                    window.revenueChart.resize();
                }
                if (window.ordersChart) {
                    window.ordersChart.resize();
                }
            },

            // 初始化迷你图表 (index1调用)
            initMiniChart: function(chartId) {
                var chartDom = document.getElementById(chartId);
                if (!chartDom) return;

                var myChart = echarts.init(chartDom);

                // 生成随机数据
                var data = [];
                for (var i = 0; i < 7; i++) {
                    data.push(Math.round(Math.random() * 100));
                }

                var option = {
                    grid: {
                        left: 0,
                        right: 0,
                        top: 0,
                        bottom: 0
                    },
                    xAxis: {
                        type: 'category',
                        show: false
                    },
                    yAxis: {
                        type: 'value',
                        show: false
                    },
                    series: [{
                        data: data,
                        type: 'line',
                        showSymbol: false,
                        itemStyle: {
                            color: '#1890ff'
                        },
                        lineStyle: {
                            width: 2
                        },
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [{
                                    offset: 0, color: 'rgba(24, 144, 255, 0.6)'
                                }, {
                                    offset: 1, color: 'rgba(24, 144, 255, 0.1)'
                                }]
                            }
                        }
                    }]
                };

                // 根据不同的卡片类型设置不同颜色
                if (chartId.includes('1') || chartId.includes('2') || chartId.includes('3')) {
                    // 收入类图表使用绿色
                    option.series[0].itemStyle.color = '#52c41a';
                    option.series[0].areaStyle.color.colorStops[0].color = 'rgba(82, 196, 26, 0.6)';
                    option.series[0].areaStyle.color.colorStops[1].color = 'rgba(82, 196, 26, 0.1)';
                } else if (chartId.includes('4') || chartId.includes('5')) {
                    // 订单数量类使用紫色
                    option.series[0].itemStyle.color = '#722ed1';
                    option.series[0].areaStyle.color.colorStops[0].color = 'rgba(114, 46, 209, 0.6)';
                    option.series[0].areaStyle.color.colorStops[1].color = 'rgba(114, 46, 209, 0.1)';
                } else if (chartId.includes('6') || chartId.includes('7')) {
                    // 欠款类使用红色
                    option.series[0].itemStyle.color = '#f5222d';
                    option.series[0].areaStyle.color.colorStops[0].color = 'rgba(245, 34, 45, 0.6)';
                    option.series[0].areaStyle.color.colorStops[1].color = 'rgba(245, 34, 45, 0.1)';
                }

                myChart.setOption(option);
                window[chartId] = myChart;
            },

            // 初始化营业收入趋势图 (index1调用)
            initRevenueChart: function() {
                var chartDom = document.getElementById('revenueChart');
                if (!chartDom) return;

                var myChart = echarts.init(chartDom);

                var option = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        }
                    },
                    legend: {
                        data: ['总收入', '实收金额', '欠款金额']
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: ['加载中...']
                    },
                    yAxis: {
                        type: 'value'
                    },
                    series: [
                        {
                            name: '总收入',
                            type: 'bar',
                            data: [0],
                            itemStyle: {
                                color: '#52c41a'
                            }
                        },
                        {
                            name: '实收金额',
                            type: 'bar',
                            data: [0],
                            itemStyle: {
                                color: '#1890ff'
                            }
                        },
                        {
                            name: '欠款金额',
                            type: 'bar',
                            data: [0],
                            itemStyle: {
                                color: '#f5222d'
                            }
                        }
                    ]
                };

                myChart.setOption(option);
                window.revenueChart = myChart;
            },

            // 初始化订单与欠款统计图 (index1调用)
            initOrdersChart: function() {
                var chartDom = document.getElementById('ordersChart');
                if (!chartDom) return;

                var myChart = echarts.init(chartDom);

                var option = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross'
                        }
                    },
                    legend: {
                        data: ['订单数量', '欠款订单数量']
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: ['加载中...']
                        }
                    ],
                    yAxis: [
                        {
                            type: 'value',
                            name: '订单数量',
                            position: 'left'
                        }
                    ],
                    series: [
                        {
                            name: '订单数量',
                            type: 'line',
                            data: [0],
                            itemStyle: {
                                color: '#722ed1'
                            }
                        },
                        {
                            name: '欠款订单数量',
                            type: 'line',
                            data: [0],
                            itemStyle: {
                                color: '#f5222d'
                            }
                        }
                    ]
                };

                myChart.setOption(option);
                window.ordersChart = myChart;
            },

            // 获取当前选择的时间范围参数 (index1调用)
            getTimeRangeParams: function() {
                // 获取筛选类型
                var activeFilter = $('.filter-btn.active');
                var filterType = activeFilter.length > 0 ? activeFilter.data('type') || 'day' : 'day';

                // 获取自定义日期范围（如果有）
                var startTime = 0;
                var endTime = 0;

                // 如果选择了自定义日期范围
                var startDateText = $('.date-placeholder:eq(0)').text();
                var endDateText = $('.date-placeholder:eq(1)').text();

                if (startDateText !== '请选择开始时间' && startDateText.length > 0) {
                    startTime = Math.floor(new Date(startDateText.replace(/-/g, '/')).getTime() / 1000);
                }

                if (endDateText !== '请选择结束时间' && endDateText.length > 0) {
                    endTime = Math.floor(new Date(endDateText.replace(/-/g, '/')).getTime() / 1000);
                }

                return {
                    start_time: startTime,
                    end_time: endTime,
                    filter_type: filterType
                };
            },

            // 加载营业收入统计数据并更新图表 (index1调用)
            loadRevenueData: function () {
                // 获取时间范围参数
                var params = Controller.api.getTimeRangeParams();

                $.ajax({
                    url: 'mycurrency/tongji/getRevenue',
                    type: 'get',
                    dataType: 'json',
                    data: params,
                    success: function (data) {
                        // 隐藏加载状态
                        $('.loading-overlay').removeClass('active');

                        // 填充总营业收入
                        $('.stat-card.type-1:eq(0) .value').html(data.total_revenue ?? '0.00');

                        // 填充时间段内的营业收入
                        $('.stat-card.type-1:eq(1) .value').html(data.period_revenue ?? '0.00');

                        // 填充时间段内的实收金额
                        $('.stat-card.type-1:eq(2) .value').html(data.period_order_payment ?? '0.00');

                        // 填充支付总笔数
                        $('.stat-card.type-1:eq(3) .value').html(data.payment_count ?? '0');

                        // 填充时间段内的支付笔数
                        $('.stat-card.type-1:eq(4) .value').html(data.period_payment_count ?? '0');

                        // 填充未付款/欠款总金额
                        $('.stat-card.type-2:eq(0) .value').html(data.total_pending_payment ?? '0.00');

                        // 填充时间段内新增欠款金额
                        $('.stat-card.type-2:eq(0) .description').text('期间新增欠款(元):' + (data.period_pending_payment ?? '0.00'));

                        // 填充历史欠款订单总笔数
                        $('.stat-card.type-2:eq(1) .value').html(data.total_pending_orders ?? '0');

                        // 更新时间段内新增欠款订单笔数到描述中
                        $('.stat-card.type-2:eq(1) .description').text('期间新增欠款订单(笔):' + (data.period_pending_orders ?? '0'));

                        // 更新图表数据（使用实际数据）
                        if (data.chart_data) {
                            Controller.api.updateChartData(data.chart_data);
                        } else {
                            Toastr.error('图表数据获取失败');
                        }
                    },
                    error: function (xhr) {
                        // 隐藏加载状态
                        $('.loading-overlay').removeClass('active');
                        Toastr.error('获取数据失败，请重试');
                    }
                });
            },

            // 更新图表数据 (index1调用)
            updateChartData: function(chartData) {
                // 更新收入趋势图
                if (window.revenueChart && chartData.categories) {
                    window.revenueChart.setOption({
                        xAxis: {
                            data: chartData.categories
                        },
                        series: [
                            {
                                name: '总收入',
                                data: chartData.total_revenue
                            },
                            {
                                name: '实收金额',
                                data: chartData.paid_amount
                            },
                            {
                                name: '欠款金额',
                                data: chartData.pending_amount
                            }
                        ]
                    });
                }

                // 更新订单数量图表
                if (window.ordersChart && chartData.categories) {
                    window.ordersChart.setOption({
                        xAxis: {
                            data: chartData.categories
                        },
                        series: [
                            {
                                name: '订单数量',
                                data: chartData.order_count
                            },
                            {
                                name: '欠款订单数量',
                                data: chartData.pending_order_count
                            }
                        ]
                    });
                }

                // 更新迷你图表
                // 根据实际数据更新每个迷你图表
                if (chartData.total_revenue && chartData.total_revenue.length > 0) {
                    // 更新收入相关的迷你图表
                    if (window.miniChart1) {
                        window.miniChart1.setOption({
                            series: [{
                                data: chartData.total_revenue
                            }]
                        });
                    }

                    if (window.miniChart2) {
                        window.miniChart2.setOption({
                            series: [{
                                data: chartData.total_revenue
                            }]
                        });
                    }

                    if (window.miniChart3) {
                        window.miniChart3.setOption({
                            series: [{
                                data: chartData.paid_amount
                            }]
                        });
                    }
                }

                if (chartData.order_count && chartData.order_count.length > 0) {
                    // 更新订单相关的迷你图表
                    if (window.miniChart4) {
                        window.miniChart4.setOption({
                            series: [{
                                data: chartData.order_count
                            }]
                        });
                    }

                    if (window.miniChart5) {
                        window.miniChart5.setOption({
                            series: [{
                                data: chartData.order_count
                            }]
                        });
                    }
                }

                if (chartData.pending_amount && chartData.pending_amount.length > 0) {
                    // 更新欠款相关的迷你图表
                    if (window.miniChart6) {
                        window.miniChart6.setOption({
                            series: [{
                                data: chartData.pending_amount
                            }]
                        });
                    }
                }

                if (chartData.pending_order_count && chartData.pending_order_count.length > 0) {
                    if (window.miniChart7) {
                        window.miniChart7.setOption({
                            series: [{
                                data: chartData.pending_order_count
                            }]
                        });
                    }
                }
            },


            // 营业额统计 - 增强版，支持更多参数
            getMoney(params){
                var myChart = echarts.init(document.getElementById('yye'));

                // 如果没有传入参数，则从UI获取筛选类型
                if (!params) {
                    params = {};
                    // 筛选类型 - 获取当前选中的日/月/年按钮
                    var filterType = 'day'; // 默认为日
                    $('.yye .nr .nav p').each(function() {
                        if ($(this).hasClass('on')) {
                            if ($(this).text() === '月') filterType = 'month';
                            else if ($(this).text() === '年') filterType = 'year';
                        }
                    });
                    params.filter_type = filterType;
                }

                // 显示加载中
                myChart.showLoading({
                    text: '数据加载中...',
                    color: '#0089FF',
                    textColor: '#fff',
                    maskColor: 'rgba(19, 58, 98, 0.8)',
                    zlevel: 0
                });

                console.log('营业额统计请求参数:', params);

                // 从后端获取数据
                $.ajax({
                    url: 'mycurrency/tongji/getRevenueStats',
                    type: 'get',
                    dataType: 'json',
                    data: params,
                    success: function(res) {
                        // 隐藏加载状态
                        myChart.hideLoading();

                        if (res.code === 1) {
                            var option = {
                                tooltip: {
                                    trigger: 'axis',
                                    formatter: function (params) {
                                        return '营业额(元)<br/>' +  params[0].value;
                                    },
                                    backgroundColor: '#133A62',
                                    borderColor: '#0E50A9',
                                    borderWidth: 1,
                                    textStyle: {
                                        color: '#fff',
                                        fontSize: 16
                                    },
                                },
                                xAxis: {
                                    type: 'category',
                                    data: res.data.categories ?? ['暂无数据'],
                                    axisLine: {
                                        lineStyle: {
                                            color: '#82A6D5'
                                        }
                                    },
                                    axisLabel: {
                                        color: '#fff',
                                        fontSize: 14
                                    },
                                    splitLine: {
                                        show: false
                                    }
                                },
                                yAxis: {
                                    type: 'value',
                                    splitLine: {
                                        show: true,
                                        lineStyle: {
                                            color: '#B2C2D3',
                                            type: 'dashed',
                                        }
                                    }
                                },
                                series: [
                                    {
                                        data: res.data.series ?? [0],
                                        type: 'line',
                                        smooth: true,
                                        lineStyle: {
                                            color: '#0089FF',
                                        },
                                    }
                                ]
                            };
                            myChart.setOption(option);

                            // 保存图表实例
                            window.revenueChart = myChart;
                        } else {
                            console.error('获取营业额统计数据失败:', res.msg);
                            if (typeof Toastr !== 'undefined') {
                                Toastr.error(res.msg || '获取营业额统计数据失败');
                            }
                        }
                    },
                    error: function() {
                        myChart.hideLoading();
                        console.error('获取营业额统计数据请求失败');
                        if (typeof Toastr !== 'undefined') {
                            Toastr.error('获取营业额统计数据请求失败，请检查网络连接');
                        }
                    }
                });
            },

            // 订单统计 - 新增方法
            getOrder(params){
                var myChart = echarts.init(document.getElementById('ddtj'));

                // 如果没有传入参数，则从UI获取筛选类型
                if (!params) {
                    params = {};
                    // 筛选类型 - 获取当前选中的日/月/年按钮
                    var filterType = 'day'; // 默认为日
                    $('.ddtj .nr .nav p').each(function() {
                        if ($(this).hasClass('on')) {
                            if ($(this).text() === '月') filterType = 'month';
                            else if ($(this).text() === '年') filterType = 'year';
                        }
                    });
                    params.filter_type = filterType;
                }

                // 显示加载中
                myChart.showLoading({
                    text: '数据加载中...',
                    color: '#0089FF',
                    textColor: '#fff',
                    maskColor: 'rgba(19, 58, 98, 0.8)',
                    zlevel: 0
                });

                console.log('订单统计请求参数:', params);

                // 从后端获取数据
                $.ajax({
                    url: 'mycurrency/tongji/getOrderStats',
                    type: 'get',
                    dataType: 'json',
                    data: params,
                    success: function(res) {
                        myChart.hideLoading();

                        if (res.code === 1) {
                            var option = {
                                tooltip: {
                                    trigger: 'axis',
                                    formatter: function (params) {
                                        return '订单数<br/>' +  params[0].value;
                                    },
                                    backgroundColor: '#133A62',
                                    borderColor: '#0E50A9',
                                    borderWidth: 1,
                                    textStyle: {
                                        color: '#fff',
                                        fontSize: 16
                                    },
                                },
                                xAxis: {
                                    type: 'category',
                                    data: res.data.categories ?? ['暂无数据'],
                                    axisLine: {
                                        lineStyle: {
                                            color: '#82A6D5'
                                        }
                                    },
                                    axisLabel: {
                                        color: '#fff',
                                        fontSize: 14
                                    },
                                    splitLine: {
                                        show: false
                                    }
                                },
                                yAxis: {
                                    type: 'value',
                                    splitLine: {
                                        show: true,
                                        lineStyle: {
                                            color: '#B2C2D3',
                                            type: 'dashed',
                                        }
                                    }
                                },
                                series: [
                                    {
                                        data: res.data.series ?? [0],
                                        type: 'line',
                                        smooth: true,
                                        lineStyle: {
                                            color: '#0089FF',
                                        },
                                    }
                                ]
                            };
                            myChart.setOption(option);

                            // 保存图表实例
                            window.orderChart = myChart;
                        } else {
                            console.error('获取订单统计数据失败:', res.msg);
                            if (typeof Toastr !== 'undefined') {
                                Toastr.error(res.msg || '获取订单统计数据失败');
                            }
                        }
                    },
                    error: function() {
                        myChart.hideLoading();
                        console.error('获取订单统计数据请求失败');
                        if (typeof Toastr !== 'undefined') {
                            Toastr.error('获取订单统计数据请求失败，请检查网络连接');
                        }
                    }
                });
            },
            init(){
                console.log('初始化 tongji.js - Controller.api.init 方法被调用');
                // 初始化其他组件
                this.getOrder()
                this.getMoney()
            },
            getOrder: function() {
                console.log('Controller.api.getOrder被调用，初始化订单统计数据');

                var myChart = echarts.init(document.getElementById('ddtj'));

                // 筛选类型 - 获取当前选中的日/月/年按钮
                var filterType = 'day'; // 默认为日
                $('.ddtj .nr .nav p').each(function() {
                    if ($(this).hasClass('on')) {
                        if ($(this).text() === '月') filterType = 'month';
                        else if ($(this).text() === '年') filterType = 'year';
                    }
                });

                // 显示加载中
                myChart.showLoading({
                    text: '数据加载中...',
                    color: '#0089FF',
                    textColor: '#fff',
                    maskColor: 'rgba(19, 58, 98, 0.8)',
                    zlevel: 0
                });

                // 从后端获取数据
                $.ajax({
                    url: 'mycurrency/tongji/getOrderStats',
                    type: 'get',
                    dataType: 'json',
                    data: {
                        filter_type: filterType
                    },
                    success: function(res) {
                        // 隐藏加载状态
                        myChart.hideLoading();

                        if (res.code === 1) {
                            var option = {
                                tooltip: {
                                    trigger: 'axis',
                                    formatter: function (params) {
                                        return '订单数<br/>' + params[0].value;
                                    },
                                    backgroundColor: '#133A62',
                                    borderColor: '#0E50A9',
                                    borderWidth: 1,
                                    textStyle: {
                                        color: '#fff',
                                        fontSize: 16
                                    },
                                },
                                xAxis: {
                                    type: 'category',
                                    data: res.data.categories ?? ['暂无数据'],
                                    axisLine: {
                                        lineStyle: {
                                            color: '#82A6D5'
                                        }
                                    },
                                    axisLabel: {
                                        color: '#fff',
                                        fontSize: 14
                                    },
                                    splitLine: {
                                        show: false
                                    }
                                },
                                yAxis: {
                                    type: 'value',
                                    splitLine: {
                                        show: true,
                                        lineStyle: {
                                            color: '#B2C2D3',
                                            type: 'dashed',
                                        }
                                    }
                                },
                                series: [
                                    {
                                        data: res.data.series ?? [0],
                                        type: 'line',
                                        smooth: true,
                                        lineStyle: {
                                            color: '#0089FF',
                                        },
                                    }
                                ]
                            };
                            myChart.setOption(option);

                            // 将图表对象保存到window对象，以便窗口大小改变时重绘
                            window.orderChart = myChart;
                        } else {
                            console.error('获取订单统计数据失败:', res.msg);

                            // 显示空数据
                            myChart.setOption({
                                xAxis: {
                                    data: ['暂无数据']
                                },
                                series: [{
                                    data: [0]
                                }]
                            });
                        }
                    },
                    error: function() {
                        // 隐藏加载状态
                        myChart.hideLoading();
                        console.error('获取订单统计数据请求失败');

                        // 显示空数据
                        myChart.setOption({
                            xAxis: {
                                data: ['请求失败']
                            },
                            series: [{
                                data: [0]
                            }]
                        });
                    }
                });
            },





            ///////////////////////////////////////////////////////////////////////////////

            /**
             * 加载柜子状态统计数据
             */
            loadCabinetStats: function() {
                // 显示加载状态
                $('.cabinet-stats-loading').show();
                $('.cabinet-stats-content').hide();

                // 获取筛选参数
                var filterData = Controller.api.getFilterParams();
                console.log('筛选参数:', filterData);

                // 发送AJAX请求获取柜子状态统计数据
                $.ajax({
                    url: 'mycurrency/tongji/getCabinetStats',
                    type: 'GET',
                    dataType: 'json',
                    timeout: 10000, // 10秒超时
                    data: filterData,
                    success: function(response) {
                        console.log('接收到的响应数据:', response);

                        // 隐藏加载状态
                        $('.cabinet-stats-loading').hide();
                        $('.cabinet-stats-content').show();

                        if (response.code === 1) {
                            console.log('准备更新统计数据:', response.data);
                            // 更新统计数据
                            Controller.api.updateCabinetStats(response.data);
                        } else {
                            console.error('获取数据失败:', response.msg);
                            Toastr.error(response.msg || '获取柜子状态统计失败');
                        }
                    },
                    error: function(xhr, status, error) {
                        // 隐藏加载状态
                        $('.cabinet-stats-loading').hide();
                        $('.cabinet-stats-content').show();

                        console.error('获取柜子状态统计失败:', error);
                        console.error('HTTP状态:', status);
                        console.error('响应详情:', xhr.responseText);
                        Toastr.error('获取柜子状态统计失败，请稍后重试');
                    }
                });
            },

            /**
             * 更新柜子状态统计显示
             * @param {Object} data 统计数据
             */
            updateCabinetStats: function(data) {
                console.log('开始更新柜子统计数据:', data);

                // 直接更新柜子数值，不使用动画
                $('#total-cabinets').text(data.total ?? 0);
                $('#online-cabinets').text(data.online ?? 0);
                $('#offline-cabinets').text(data.offline ?? 0);

                // 计算在线率
                var onlineRate = data.total > 0 ? ((data.online / data.total) * 100).toFixed(1) : 0;

                // 直接更新在线率和进度条，不使用延迟
                $('#online-rate').text(onlineRate + '%');
                $('.online-progress-bar').css('width', onlineRate + '%');

                // 更新球杆统计数据
                $('#stick-total').text(data.stick_total ?? 0);
                $('#stick-available').text(data.stick_available ?? 0);
                $('#stick-in-use').text(data.stick_in_use ?? 0);

                console.log('柜子和球杆统计更新完成:', data);
            },

            /**
             * 初始化AOS动画库
             */
            initAOS: function() {
                // 初始化AOS动画库
                if (typeof AOS !== 'undefined') {
                    AOS.init({
                        duration: 800,
                        easing: 'ease-in-out',
                        once: true
                    });
                }
            },

            /**
             * 初始化页面UI
             */
            initPageUI: function() {
                // 确保内容区域初始隐藏，加载状态显示
                $('.cabinet-stats-content').hide();
                $('.cabinet-stats-loading').show();
            },

            /**
             * 刷新柜子状态统计数据
             */
            refreshCabinetStats: function() {
                Controller.api.loadCabinetStats();
                Controller.api.loadPlatformStats();
            },

            /**
             * 初始化筛选功能
             */
            initFilter: function() {
                // 加载省份列表
                Controller.api.loadProvinceList();

                // 加载所有代理商列表
                Controller.api.loadAgentList({});

                // 加载所有门店列表
                Controller.api.loadStoreList({});

                // 绑定筛选事件
                Controller.api.bindFilterEvents();
            },

            /**
             * 绑定筛选相关事件
             */
            bindFilterEvents: function() {
                // 省份选择变化
                $('#province-select').on('change', function() {
                    var provinceId = $(this).val();
                    Controller.api.onProvinceChange(provinceId);
                });

                // 城市选择变化
                $('#city-select').on('change', function() {
                    var cityId = $(this).val();
                    Controller.api.onCityChange(cityId);
                });

                // 区县选择变化
                $('#district-select').on('change', function() {
                    var districtId = $(this).val();
                    Controller.api.onDistrictChange(districtId);
                });

                // 代理商选择变化
                $('#agent-select').on('change', function() {
                    var agentId = $(this).val();
                    Controller.api.onAgentChange(agentId);
                });

                // 重置按钮
                $('#reset-filter').on('click', function() {
                    Controller.api.resetFilter();
                });

                // 查询按钮
                $('#apply-filter').on('click', function() {
                    Controller.api.applyFilter();
                });
            },

            /**
             * 加载省份列表
             */
            loadProvinceList: function() {
                $.ajax({
                    url: 'mycurrency/tongji/getProvinceList',
                    type: 'GET',
                    dataType: 'json',
                    success: function(response) {
                        if (response && response.code === 1) {
                            Controller.api.populateSelect('#province-select', response.data, '请选择省份');
                        }
                    },
                    error: function() {
                        console.error('加载省份列表失败');
                    }
                });
            },

            /**
             * 省份选择变化处理
             */
            onProvinceChange: function(provinceId) {
                // 清空并禁用下级选择框
                Controller.api.clearAndDisableSelect('#city-select', '请选择城市');
                Controller.api.clearAndDisableSelect('#district-select', '请选择区县');

                if (provinceId) {
                    // 加载城市列表
                    Controller.api.loadCityList(provinceId);
                    // 重新加载该省份的代理商（保持当前选择）
                    Controller.api.reloadAgentList({province_id: provinceId});
                    // 重新加载该省份的门店（保持当前选择）
                    Controller.api.reloadStoreList({province_id: provinceId});
                } else {
                    // 重新加载所有代理商（保持当前选择）
                    Controller.api.reloadAgentList({});
                    // 重新加载所有门店（保持当前选择）
                    Controller.api.reloadStoreList({});
                }
            },

            /**
             * 城市选择变化处理
             */
            onCityChange: function(cityId) {
                // 清空并禁用下级选择框
                Controller.api.clearAndDisableSelect('#district-select', '请选择区县');

                var provinceId = $('#province-select').val();

                if (cityId) {
                    // 加载区县列表
                    Controller.api.loadDistrictList(cityId);
                    // 重新加载该城市的代理商（保持当前选择）
                    Controller.api.reloadAgentList({province_id: provinceId, city_id: cityId});
                    // 重新加载该城市的门店（保持当前选择）
                    Controller.api.reloadStoreList({province_id: provinceId, city_id: cityId});
                } else if (provinceId) {
                    // 重新加载该省份的代理商（保持当前选择）
                    Controller.api.reloadAgentList({province_id: provinceId});
                    // 重新加载该省份的门店（保持当前选择）
                    Controller.api.reloadStoreList({province_id: provinceId});
                } else {
                    // 重新加载所有代理商（保持当前选择）
                    Controller.api.reloadAgentList({});
                    // 重新加载所有门店（保持当前选择）
                    Controller.api.reloadStoreList({});
                }
            },

            /**
             * 区县选择变化处理
             */
            onDistrictChange: function(districtId) {
                var provinceId = $('#province-select').val();
                var cityId = $('#city-select').val();

                if (districtId) {
                    // 重新加载该区县的代理商（保持当前选择）
                    Controller.api.reloadAgentList({province_id: provinceId, city_id: cityId, area_id: districtId});
                    // 重新加载该区县的门店（保持当前选择）
                    Controller.api.reloadStoreList({province_id: provinceId, city_id: cityId, area_id: districtId});
                } else if (cityId) {
                    // 重新加载该城市的代理商（保持当前选择）
                    Controller.api.reloadAgentList({province_id: provinceId, city_id: cityId});
                    // 重新加载该城市的门店（保持当前选择）
                    Controller.api.reloadStoreList({province_id: provinceId, city_id: cityId});
                } else if (provinceId) {
                    // 重新加载该省份的代理商（保持当前选择）
                    Controller.api.reloadAgentList({province_id: provinceId});
                    // 重新加载该省份的门店（保持当前选择）
                    Controller.api.reloadStoreList({province_id: provinceId});
                } else {
                    // 重新加载所有代理商（保持当前选择）
                    Controller.api.reloadAgentList({});
                    // 重新加载所有门店（保持当前选择）
                    Controller.api.reloadStoreList({});
                }
            },

            /**
             * 代理商选择变化处理
             */
            onAgentChange: function(agentId) {
                if (agentId) {
                    // 重新加载该代理商的门店（保持当前选择）
                    Controller.api.reloadStoreList({agent_id: agentId});
                } else {
                    // 根据地区重新加载门店（保持当前选择）
                    var params = Controller.api.getAreaParams();
                    Controller.api.reloadStoreList(params);
                }
            },

            /**
             * 加载城市列表
             */
            loadCityList: function(provinceId) {
                $.ajax({
                    url: 'mycurrency/tongji/getCityList',
                    type: 'GET',
                    data: {province_id: provinceId},
                    dataType: 'json',
                    success: function(response) {
                        if (response && response.code === 1) {
                            Controller.api.populateSelect('#city-select', response.data, '请选择城市');
                            $('#city-select').prop('disabled', false);
                        }
                    },
                    error: function() {
                        console.error('加载城市列表失败');
                    }
                });
            },

            /**
             * 加载区县列表
             */
            loadDistrictList: function(cityId) {
                $.ajax({
                    url: 'mycurrency/tongji/getDistrictList',
                    type: 'GET',
                    data: {city_id: cityId},
                    dataType: 'json',
                    success: function(response) {
                        if (response && response.code === 1) {
                            Controller.api.populateSelect('#district-select', response.data, '请选择区县');
                            $('#district-select').prop('disabled', false);
                        }
                    },
                    error: function() {
                        console.error('加载区县列表失败');
                    }
                });
            },

            /**
             * 加载代理商列表
             */
            loadAgentList: function(params) {
                $.ajax({
                    url: 'mycurrency/tongji/getAgentList',
                    type: 'GET',
                    data: params || {},
                    dataType: 'json',
                    success: function(response) {
                        if (response && response.code === 1) {
                            Controller.api.populateSelect('#agent-select', response.data, '请选择代理商');
                        }
                    },
                    error: function() {
                        console.error('加载代理商列表失败');
                    }
                });
            },

            /**
             * 加载门店列表
             */
            loadStoreList: function(params) {
                $.ajax({
                    url: 'mycurrency/tongji/getStoreList',
                    type: 'GET',
                    data: params || {},
                    dataType: 'json',
                    success: function(response) {
                        if (response && response.code === 1) {
                            Controller.api.populateSelect('#store-select', response.data, '请选择门店');
                        }
                    },
                    error: function() {
                        console.error('加载门店列表失败');
                    }
                });
            },

            /**
             * 填充下拉框选项
             */
            populateSelect: function(selector, data, placeholder) {
                var $select = $(selector);
                $select.empty();
                $select.append('<option value="">' + placeholder + '</option>');

                if (data && data.length > 0) {
                    $.each(data, function(index, item) {
                        $select.append('<option value="' + item.value + '">' + item.label + '</option>');
                    });
                }
            },

            /**
             * 清空并禁用下拉框
             */
            clearAndDisableSelect: function(selector, placeholder) {
                var $select = $(selector);
                $select.empty();
                $select.append('<option value="">' + placeholder + '</option>');
                $select.prop('disabled', true);
            },

            /**
             * 清空下拉框
             */
            clearSelect: function(selector, placeholder) {
                var $select = $(selector);
                $select.empty();
                $select.append('<option value="">' + placeholder + '</option>');
            },

            /**
             * 获取当前地区参数
             */
            getAreaParams: function() {
                var params = {};
                var provinceId = $('#province-select').val();
                var cityId = $('#city-select').val();
                var districtId = $('#district-select').val();

                if (provinceId) params.province_id = provinceId;
                if (cityId) params.city_id = cityId;
                if (districtId) params.area_id = districtId;

                return params;
            },

            /**
             * 重置筛选条件
             */
            resetFilter: function() {
                // 重置所有下拉框
                $('#province-select').val('');
                $('#city-select').val('').prop('disabled', true);
                $('#district-select').val('').prop('disabled', true);
                $('#agent-select').val('');
                $('#store-select').val('');

                // 清空下拉框内容
                Controller.api.clearAndDisableSelect('#city-select', '请选择城市');
                Controller.api.clearAndDisableSelect('#district-select', '请选择区县');

                // 重新加载所有代理商列表
                Controller.api.loadAgentList({});

                // 重新加载所有门店列表
                Controller.api.loadStoreList({});

                // 重新加载数据
                Controller.api.loadCabinetStats();
                Controller.api.loadPlatformStats();
                // 刷新图表数据
                Controller.api.loadChartData();
                // 刷新当前排行榜数据
                Controller.api.loadCurrentRankingData();
                // 刷新地图数据
                if (Controller.api.map && Controller.api.markerLayer) {
                    Controller.api.loadMapData();
                }
            },

            /**
             * 应用筛选条件
             */
            applyFilter: function() {
                Controller.api.loadCabinetStats();
                Controller.api.loadPlatformStats();
                // 刷新图表数据
                Controller.api.loadChartData();
                // 刷新当前排行榜数据
                Controller.api.loadCurrentRankingData();
                // 只有在地图已初始化的情况下才更新地图数据
                if (Controller.api.map && Controller.api.markerLayer) {
                    Controller.api.loadMapData();
                }
            },

            /**
             * 获取筛选参数
             */
            getFilterParams: function() {
                var params = {};

                var provinceId = $('#province-select').val();
                var cityId = $('#city-select').val();
                var districtId = $('#district-select').val();
                var agentId = $('#agent-select').val();
                var storeId = $('#store-select').val();

                if (storeId) {
                    params.store_id = storeId;
                } else if (agentId) {
                    params.agent_id = agentId;
                } else {
                    if (provinceId) params.province_id = provinceId;
                    if (cityId) params.city_id = cityId;
                    if (districtId) params.area_id = districtId;
                }

                return params;
            },

            /**
             * 重新加载代理商列表（保持当前选择）
             */
            reloadAgentList: function(params) {
                var currentValue = $('#agent-select').val(); // 保存当前选择

                $.ajax({
                    url: 'mycurrency/tongji/getAgentList',
                    type: 'GET',
                    data: params || {},
                    dataType: 'json',
                    success: function(response) {
                        if (response && response.code === 1) {
                            Controller.api.populateSelect('#agent-select', response.data, '请选择代理商');

                            // 尝试恢复之前的选择
                            if (currentValue) {
                                var $option = $('#agent-select option[value="' + currentValue + '"]');
                                if ($option.length > 0) {
                                    $('#agent-select').val(currentValue);
                                }
                            }
                        }
                    },
                    error: function() {
                        console.error('重新加载代理商列表失败');
                    }
                });
            },

            /**
             * 重新加载门店列表（保持当前选择）
             */
            reloadStoreList: function(params) {
                var currentValue = $('#store-select').val(); // 保存当前选择

                $.ajax({
                    url: 'mycurrency/tongji/getStoreList',
                    type: 'GET',
                    data: params || {},
                    dataType: 'json',
                    success: function(response) {
                        if (response && response.code === 1) {
                            Controller.api.populateSelect('#store-select', response.data, '请选择门店');

                            // 尝试恢复之前的选择
                            if (currentValue) {
                                var $option = $('#store-select option[value="' + currentValue + '"]');
                                if ($option.length > 0) {
                                    $('#store-select').val(currentValue);
                                }
                            }
                        }
                    },
                    error: function() {
                        console.error('重新加载门店列表失败');
                    }
                });
            },

            /**
             * 初始化图表
             */
            initCharts: function() {
                console.log('开始初始化图表...');

                // 确保容器存在且有尺寸
                var orderContainer = document.getElementById('order-chart');
                var revenueContainer = document.getElementById('revenue-chart');

                if (!orderContainer || !revenueContainer) {
                    console.error('图表容器不存在');
                    return;
                }

                // 确保容器有尺寸
                if (orderContainer.offsetWidth === 0 || orderContainer.offsetHeight === 0) {
                    console.log('容器尺寸为0，延迟初始化');
                    setTimeout(Controller.api.initCharts, 500);
                    return;
                }

                console.log('容器尺寸正常，开始初始化图表');
                console.log('订单图表容器尺寸:', orderContainer.offsetWidth, 'x', orderContainer.offsetHeight);
                console.log('营业额图表容器尺寸:', revenueContainer.offsetWidth, 'x', revenueContainer.offsetHeight);

                // 初始化订单数量图表
                if (Controller.api.orderChart) {
                    Controller.api.orderChart.dispose();
                }
                Controller.api.orderChart = echarts.init(orderContainer);

                // 初始化营业额图表
                if (Controller.api.revenueChart) {
                    Controller.api.revenueChart.dispose();
                }
                Controller.api.revenueChart = echarts.init(revenueContainer);

                // 立即调整图表尺寸
                setTimeout(function() {
                    if (Controller.api.orderChart) {
                        Controller.api.orderChart.resize();
                    }
                    if (Controller.api.revenueChart) {
                        Controller.api.revenueChart.resize();
                    }
                }, 100);

                // 监听窗口大小变化
                if (!Controller.api.resizeListener) {
                    Controller.api.resizeListener = function() {
                        if (Controller.api.orderChart) {
                            Controller.api.orderChart.resize();
                        }
                        if (Controller.api.revenueChart) {
                            Controller.api.revenueChart.resize();
                        }
                    };
                    window.addEventListener('resize', Controller.api.resizeListener);
                }

                console.log('图表初始化完成');
            },

            /**
             * 初始化时间筛选功能
             */
            initTimeFilter: function() {
                // 绑定时间筛选按钮事件
                $('.time-btn').on('click', function() {
                    $('.time-btn').removeClass('active');
                    $(this).addClass('active');

                    var type = $(this).data('type');
                    if (type === 'custom') {
                        $('#custom-time-range').addClass('active');
                    } else {
                        $('#custom-time-range').removeClass('active');
                        Controller.api.applyTimeFilter(type);
                    }
                });

                // 绑定自定义时间查询按钮
                $('#apply-time-filter').on('click', function() {
                    var startTime = $('#start-time').val();
                    var endTime = $('#end-time').val();

                    if (startTime && endTime) {
                        var startTimestamp = new Date(startTime).getTime() / 1000;
                        var endTimestamp = new Date(endTime).getTime() / 1000;

                        if (startTimestamp >= endTimestamp) {
                            Toastr.error('开始时间必须小于结束时间');
                            return;
                        }

                        Controller.api.loadChartData('day', startTimestamp, endTimestamp);
                    } else {
                        Toastr.error('请选择开始时间和结束时间');
                    }
                });
            },

            /**
             * 应用时间筛选
             */
            applyTimeFilter: function(type) {
                var now = new Date();
                var startTime, endTime;
                var filterType = 'hour'; // 默认按小时

                switch (type) {
                    case 'today':
                        startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime() / 1000;
                        endTime = now.getTime() / 1000;
                        filterType = 'hour'; // 今天按小时显示
                        break;
                    case 'yesterday':
                        var yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                        startTime = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate()).getTime() / 1000;
                        endTime = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59).getTime() / 1000;
                        filterType = 'hour'; // 昨天按小时显示
                        break;
                    case 'recent-3':
                        startTime = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000).getTime() / 1000;
                        endTime = now.getTime() / 1000;
                        filterType = 'day'; // 最近3天按天显示
                        break;
                    case 'recent-7':
                        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).getTime() / 1000;
                        endTime = now.getTime() / 1000;
                        filterType = 'day'; // 最近7天按天显示
                        break;
                    case 'recent-30':
                        startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).getTime() / 1000;
                        endTime = now.getTime() / 1000;
                        filterType = 'day'; // 最近30天按天显示
                        break;
                }

                console.log('时间筛选:', type, 'filterType:', filterType, 'startTime:', startTime, 'endTime:', endTime);
                Controller.api.loadChartData(filterType, startTime, endTime);
            },

            /**
             * 加载图表数据
             */
            loadChartData: function(filterType, startTime, endTime) {
                // 获取筛选参数
                var filterParams = Controller.api.getFilterParams();

                // 添加时间参数
                var params = Object.assign({}, filterParams);

                // 只有在明确指定了筛选类型时才添加filter_type参数
                if (filterType) {
                    params.filter_type = filterType;
                }

                if (startTime && endTime) {
                    params.start_time = startTime;
                    params.end_time = endTime;
                }

                console.log('加载图表数据，参数:', params);

                // 加载订单数量图表数据
                Controller.api.loadOrderChartData(params);

                // 加载营业额图表数据
                Controller.api.loadRevenueChartData(params);
            },

            /**
             * 加载订单数量图表数据
             */
            loadOrderChartData: function(params) {
                $('#order-chart-loading').show();

                $.ajax({
                    url: 'mycurrency/tongji/getOrderChartData',
                    type: 'GET',
                    dataType: 'json',
                    data: params,
                    success: function(response) {
                        $('#order-chart-loading').hide();

                        if (response.code === 1) {
                            Controller.api.updateOrderChart(response.data);
                        } else {
                            Toastr.error(response.msg || '获取订单统计数据失败');
                        }
                    },
                    error: function() {
                        $('#order-chart-loading').hide();
                        Toastr.error('获取订单统计数据失败，请稍后重试');
                    }
                });
            },

            /**
             * 加载营业额图表数据
             */
            loadRevenueChartData: function(params) {
                $('#revenue-chart-loading').show();

                $.ajax({
                    url: 'mycurrency/tongji/getRevenueChartData',
                    type: 'GET',
                    dataType: 'json',
                    data: params,
                    success: function(response) {
                        $('#revenue-chart-loading').hide();

                        if (response.code === 1) {
                            Controller.api.updateRevenueChart(response.data);
                        } else {
                            Toastr.error(response.msg || '获取营业额统计数据失败');
                        }
                    },
                    error: function() {
                        $('#revenue-chart-loading').hide();
                        Toastr.error('获取营业额统计数据失败，请稍后重试');
                    }
                });
            },

            /**
             * 更新订单数量图表
             */
            updateOrderChart: function(data) {
                if (!Controller.api.orderChart) {
                    console.log('订单图表未初始化，重新初始化');
                    Controller.api.initCharts();
                    if (!Controller.api.orderChart) return;
                }

                console.log('更新订单图表数据:', data);

                var option = {
                    title: {
                        text: '订单数量统计',
                        textStyle: {
                            color: '#ffffff',
                            fontSize: 16
                        },
                        left: 'center'
                    },
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        borderColor: '#64b5f6',
                        textStyle: {
                            color: '#ffffff'
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: data.categories || [],
                        axisLine: {
                            lineStyle: {
                                color: 'rgba(255, 255, 255, 0.3)'
                            }
                        },
                        axisLabel: {
                            color: 'rgba(255, 255, 255, 0.7)'
                        }
                    },
                    yAxis: {
                        type: 'value',
                        axisLine: {
                            lineStyle: {
                                color: 'rgba(255, 255, 255, 0.3)'
                            }
                        },
                        axisLabel: {
                            color: 'rgba(255, 255, 255, 0.7)',
                            formatter: '¥{value}'
                        },
                        splitLine: {
                            lineStyle: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        }
                    },
                    series: [{
                        name: '订单数量',
                        type: 'line',
                        data: data.series || [],
                        smooth: true,
                        lineStyle: {
                            color: '#64b5f6',
                            width: 3
                        },
                        itemStyle: {
                            color: '#64b5f6'
                        },
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [{
                                    offset: 0, color: 'rgba(100, 181, 246, 0.3)'
                                }, {
                                    offset: 1, color: 'rgba(100, 181, 246, 0.05)'
                                }]
                            }
                        }
                    }]
                };

                Controller.api.orderChart.setOption(option);

                // 确保图表正确显示
                setTimeout(function() {
                    if (Controller.api.orderChart) {
                        Controller.api.orderChart.resize();
                    }
                }, 100);
            },

            /**
             * 更新营业额图表
             */
            updateRevenueChart: function(data) {
                if (!Controller.api.revenueChart) {
                    console.log('营业额图表未初始化，重新初始化');
                    Controller.api.initCharts();
                    if (!Controller.api.revenueChart) return;
                }

                console.log('更新营业额图表数据:', data);

                var option = {
                    title: {
                        text: '营业额统计',
                        textStyle: {
                            color: '#ffffff',
                            fontSize: 16
                        },
                        left: 'center'
                    },
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        borderColor: '#4caf50',
                        textStyle: {
                            color: '#ffffff'
                        },
                        formatter: function(params) {
                            var result = params[0].name + '<br/>';
                            result += params[0].seriesName + ': ¥' + params[0].value.toFixed(2);
                            return result;
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: data.categories || [],
                        axisLine: {
                            lineStyle: {
                                color: 'rgba(255, 255, 255, 0.3)'
                            }
                        },
                        axisLabel: {
                            color: 'rgba(255, 255, 255, 0.7)'
                        }
                    },
                    yAxis: {
                        type: 'value',
                        axisLine: {
                            lineStyle: {
                                color: 'rgba(255, 255, 255, 0.3)'
                            }
                        },
                        axisLabel: {
                            color: 'rgba(255, 255, 255, 0.7)',
                            formatter: '¥{value}'
                        },
                        splitLine: {
                            lineStyle: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        }
                    },
                    series: [{
                        name: '营业额',
                        type: 'line',
                        data: data.series || [],
                        smooth: true,
                        lineStyle: {
                            color: '#4caf50',
                            width: 3
                        },
                        itemStyle: {
                            color: '#4caf50'
                        },
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [{
                                    offset: 0, color: 'rgba(76, 175, 80, 0.3)'
                                }, {
                                    offset: 1, color: 'rgba(76, 175, 80, 0.05)'
                                }]
                            }
                        }
                    }]
                };

                Controller.api.revenueChart.setOption(option);

                // 确保图表正确显示
                setTimeout(function() {
                    if (Controller.api.revenueChart) {
                        Controller.api.revenueChart.resize();
                    }
                }, 100);
            },

            /**
             * 初始化地图
             */
            initMap: function() {
                console.log('=== 开始初始化地图 ===');

                // 详细检查腾讯地图API
                console.log('检查window.TMap:', typeof window.TMap);
                if (window.TMap) {
                    console.log('检查TMap.Map:', typeof window.TMap.Map);
                    console.log('检查TMap.LatLng:', typeof window.TMap.LatLng);
                    console.log('检查TMap.MultiMarker:', typeof window.TMap.MultiMarker);
                }

                if (!isTencentMapReady()) {
                    console.error('腾讯地图API未完全加载');
                    $('#map-loading').hide();
                    $('#store-map').html('<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: rgba(255,255,255,0.7); text-align: center;"><div><i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 10px;"></i><br>地图API未完全加载<br><small>请刷新页面重试</small></div></div>');
                    return;
                }

                // 确保地图容器存在
                var mapContainer = document.getElementById('store-map');
                if (!mapContainer) {
                    console.error('地图容器不存在');
                    return;
                }

                // 确保容器有尺寸
                console.log('地图容器尺寸:', mapContainer.offsetWidth, 'x', mapContainer.offsetHeight);
                if (mapContainer.offsetWidth === 0 || mapContainer.offsetHeight === 0) {
                    console.log('地图容器尺寸为0，延迟初始化');
                    setTimeout(function() {
                        Controller.api.initMap();
                    }, 500);
                    return;
                }

                console.log('开始创建地图实例...');

                // 初始化地图
                try {
                    // 清空容器内容
                    mapContainer.innerHTML = '';

                    console.log('创建TMap.LatLng...');
                    var center = new window.TMap.LatLng(36.0, 118.0);
                    console.log('中心点创建成功:', center);

                    console.log('创建TMap.Map...');

                    // 使用最简单的配置
                    Controller.api.map = new window.TMap.Map(mapContainer, {
                        center: center,
                        zoom: 7
                    });
                    console.log('地图实例创建成功:', Controller.api.map);

                    console.log('创建MultiMarker...');
                    Controller.api.markerLayer = new window.TMap.MultiMarker({
                        map: Controller.api.map,
                        styles: {
                            'default': new window.TMap.MarkerStyle({
                                width: 25,
                                height: 35,
                                anchor: { x: 12, y: 35 },
                                src: 'data:image/svg+xml;base64,' + btoa(`
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="35" viewBox="0 0 25 35">
                                        <path d="M12.5 0C5.6 0 0 5.6 0 12.5c0 12.5 12.5 22.5 12.5 22.5s12.5-10 12.5-22.5C25 5.6 19.4 0 12.5 0z" fill="#64b5f6"/>
                                        <circle cx="12.5" cy="12.5" r="6" fill="#ffffff"/>
                                        <circle cx="12.5" cy="12.5" r="3" fill="#1976d2"/>
                                    </svg>
                                `)
                            }),
                            'online': new window.TMap.MarkerStyle({
                                width: 25,
                                height: 35,
                                anchor: { x: 12, y: 35 },
                                src: 'data:image/svg+xml;base64,' + btoa(`
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="35" viewBox="0 0 25 35">
                                        <path d="M12.5 0C5.6 0 0 5.6 0 12.5c0 12.5 12.5 22.5 12.5 22.5s12.5-10 12.5-22.5C25 5.6 19.4 0 12.5 0z" fill="#4caf50"/>
                                        <circle cx="12.5" cy="12.5" r="6" fill="#ffffff"/>
                                        <circle cx="12.5" cy="12.5" r="3" fill="#2e7d32"/>
                                    </svg>
                                `)
                            }),
                            'offline': new window.TMap.MarkerStyle({
                                width: 25,
                                height: 35,
                                anchor: { x: 12, y: 35 },
                                src: 'data:image/svg+xml;base64,' + btoa(`
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="35" viewBox="0 0 25 35">
                                        <path d="M12.5 0C5.6 0 0 5.6 0 12.5c0 12.5 12.5 22.5 12.5 22.5s12.5-10 12.5-22.5C25 5.6 19.4 0 12.5 0z" fill="#f44336"/>
                                        <circle cx="12.5" cy="12.5" r="6" fill="#ffffff"/>
                                        <circle cx="12.5" cy="12.5" r="3" fill="#c62828"/>
                                    </svg>
                                `)
                            })
                        },
                        geometries: []
                    });
                    console.log('MultiMarker创建成功:', Controller.api.markerLayer);

                    console.log('创建InfoWindow...');
                    Controller.api.infoWindow = new window.TMap.InfoWindow({
                        map: Controller.api.map,
                        position: new window.TMap.LatLng(36.0, 118.0),
                        offset: { x: 0, y: -35 }
                    });
                    console.log('InfoWindow创建成功:', Controller.api.infoWindow);

                    // 绑定标记点击事件
                    Controller.api.markerLayer.on('click', function(evt) {
                        Controller.api.onMarkerClick(evt);
                    });

                    console.log('=== 地图初始化完成 ===');

                    // 隐藏加载提示
                    $('#map-loading').hide();

                } catch (error) {
                    console.error('=== 地图初始化失败 ===');
                    console.error('错误详情:', error);
                    console.error('错误堆栈:', error.stack);

                    $('#map-loading').hide();
                    $('#store-map').html('<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: rgba(255,255,255,0.7); text-align: center;"><div><i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 10px;"></i><br>地图初始化失败<br><small>' + error.message + '</small></div></div>');
                }
            },

            /**
             * 加载地图数据
             */
            loadMapData: function() {
                console.log('开始加载地图数据...');

                // 检查地图是否已初始化
                if (!Controller.api.map || !Controller.api.markerLayer) {
                    console.log('地图未初始化，跳过数据加载');
                    return;
                }
                // 关闭可能已打开的窗口
                if(Controller.api.infoWindow) Controller.api.infoWindow.close();
                // 显示加载状态
                $('#map-loading').show();

                // 获取筛选参数
                var filterParams = Controller.api.getFilterParams();
                console.log('地图数据筛选参数:', filterParams);

                // 发送AJAX请求获取门店地图数据
                $.ajax({
                    url: 'mycurrency/tongji/getStoreMapData',
                    type: 'GET',
                    dataType: 'json',
                    timeout: 10000, // 10秒超时
                    data: filterParams,
                    success: function(response) {
                        console.log('接收到的地图数据:', response);

                        // 隐藏加载状态
                        $('#map-loading').hide();

                        if (response.code === 1) {
                            console.log('准备更新地图标记:', response.data);
                            // 更新地图标记
                            Controller.api.updateMapMarkers(response.data);
                        } else {
                            console.error('获取地图数据失败:', response.msg);
                            Toastr.error(response.msg || '获取门店地图数据失败');
                        }
                    },
                    error: function(xhr, status, error) {
                        // 隐藏加载状态
                        $('#map-loading').hide();

                        console.error('获取门店地图数据失败:', error);
                        console.error('HTTP状态:', status);
                        console.error('响应详情:', xhr.responseText);
                        Toastr.error('获取门店地图数据失败，请稍后重试');
                    }
                });
            },

            /**
             * 更新地图标记
             */
            updateMapMarkers: function(storeData) {
                console.log('开始更新地图标记，门店数量:', storeData.length);

                if (!Controller.api.markerLayer) {
                    console.error('标记图层未初始化');
                    return;
                }

                // 清除现有标记
                Controller.api.markerLayer.setGeometries([]);

                if (!storeData || storeData.length === 0) {
                    console.log('没有门店数据，清空地图标记');
                    return;
                }

                // 准备标记数据
                var markers = [];
                var bounds = new window.TMap.LatLngBounds();

                storeData.forEach(function(store) {
                    if (store.longitude && store.latitude) {
                        var position = new window.TMap.LatLng(store.latitude, store.longitude);

                        // 根据设备在线状态选择标记样式
                        var styleId = 'default';
                        if (store.stats && store.stats.device_total > 0) {
                            var onlineRate = store.stats.device_online / store.stats.device_total;
                            if (onlineRate >= 0.8) {
                                styleId = 'online';
                            } else if (onlineRate <= 0.2) {
                                styleId = 'offline';
                            }
                        }

                        markers.push({
                            id: 'store_' + store.id,
                            styleId: styleId,
                            position: position,
                            properties: {
                                storeData: store
                            }
                        });

                        // 扩展边界
                        bounds.extend(position);
                    }
                });

                // 设置标记
                Controller.api.markerLayer.setGeometries(markers);

                // 调整地图视野以包含所有标记
                if (markers.length > 0) {
                    if (markers.length === 1) {
                        // 只有一个标记时，设置中心点和合适的缩放级别
                        Controller.api.map.setCenter(markers[0].position);
                        Controller.api.map.setZoom(15);
                    } else {
                        // 多个标记时，调整视野以包含所有标记
                        Controller.api.map.fitBounds(bounds, {
                            padding: 50
                        });
                    }
                }

                console.log('地图标记更新完成，标记数量:', markers.length);
            },

            /**
             * 标记点击事件处理
             */
            onMarkerClick: function(evt) {
                console.log('标记被点击:', evt);

                var geometry = evt.geometry;
                var storeData = geometry.properties.storeData;

                if (!storeData) {
                    console.error('门店数据不存在');
                    return;
                }

                // 构建信息窗口内容
                var infoContent = Controller.api.buildInfoWindowContent(storeData);

                // 设置信息窗口位置和内容
                Controller.api.infoWindow.setPosition(geometry.position);
                Controller.api.infoWindow.setContent(infoContent);
                Controller.api.infoWindow.open();

                console.log('信息窗口已打开，门店:', storeData.name);
            },

            /**
             * 构建信息窗口内容
             */
            buildInfoWindowContent: function(storeData) {
                console.log('构建信息窗口内容，门店数据:', storeData);

                var stats = storeData.stats || {};
                console.log('门店统计数据:', stats);

                // 计算在线率
                var onlineRate = 0;
                if (stats.device_total > 0) {
                    onlineRate = ((stats.device_online / stats.device_total) * 100).toFixed(1);
                }

                // 计算球杆使用率
                var stickUsageRate = 0;
                if (stats.stick_total > 0) {
                    stickUsageRate = ((stats.stick_in_use / stats.stick_total) * 100).toFixed(1);
                }

                // 使用简化的HTML结构，避免复杂的CSS类
                var content = '<div style="padding: 15px; background: #1a2332; color: #ffffff; border-radius: 8px; min-width: 280px; font-family: Microsoft YaHei;">' +
                    '<div style="font-size: 16px; font-weight: bold; color: #64b5f6; margin-bottom: 10px; border-bottom: 1px solid rgba(255,255,255,0.2); padding-bottom: 8px;">' +
                    '<i class="fas fa-store"></i> ' + (storeData.name || storeData.store_name || '未知门店') +
                    '</div>' +
                    '<div style="color: rgba(255,255,255,0.8); margin-bottom: 15px; font-size: 14px;">' +
                    '<i class="fas fa-map-marker-alt"></i> ' + (storeData.address || '地址未知') +
                    '</div>' +
                    '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">' +
                    '<div style="background: rgba(255,255,255,0.1); padding: 8px; border-radius: 6px; text-align: center;">' +
                    '<div style="font-size: 18px; font-weight: bold; color: #64b5f6;">' + (stats.device_total || 0) + '</div>' +
                    '<div style="font-size: 12px; color: rgba(255,255,255,0.7);">总设备数</div>' +
                    '</div>' +
                    '<div style="background: rgba(255,255,255,0.1); padding: 8px; border-radius: 6px; text-align: center;">' +
                    '<div style="font-size: 18px; font-weight: bold; color: #4caf50;">' + (stats.device_online || 0) + '</div>' +
                    '<div style="font-size: 12px; color: rgba(255,255,255,0.7);">在线设备</div>' +
                    '</div>' +
                    '<div style="background: rgba(255,255,255,0.1); padding: 8px; border-radius: 6px; text-align: center;">' +
                    '<div style="font-size: 18px; font-weight: bold; color: #f44336;">' + (stats.device_offline || 0) + '</div>' +
                    '<div style="font-size: 12px; color: rgba(255,255,255,0.7);">离线设备</div>' +
                    '</div>' +
                    '<div style="background: rgba(255,255,255,0.1); padding: 8px; border-radius: 6px; text-align: center;">' +
                    '<div style="font-size: 18px; font-weight: bold; color: #ff9800;">' + onlineRate + '%</div>' +
                    '<div style="font-size: 12px; color: rgba(255,255,255,0.7);">设备在线率</div>' +
                    '</div>' +
                    '<div style="background: rgba(255,255,255,0.1); padding: 8px; border-radius: 6px; text-align: center;">' +
                    '<div style="font-size: 18px; font-weight: bold; color: #ff9800;">' + (stats.stick_total || 0) + '</div>' +
                    '<div style="font-size: 12px; color: rgba(255,255,255,0.7);">球杆总数</div>' +
                    '</div>' +
                    '<div style="background: rgba(255,255,255,0.1); padding: 8px; border-radius: 6px; text-align: center;">' +
                    '<div style="font-size: 18px; font-weight: bold; color: #4caf50;">' + (stats.stick_available || 0) + '</div>' +
                    '<div style="font-size: 12px; color: rgba(255,255,255,0.7);">可用球杆</div>' +
                    '</div>' +
                    '<div style="background: rgba(255,255,255,0.1); padding: 8px; border-radius: 6px; text-align: center;">' +
                    '<div style="font-size: 18px; font-weight: bold; color: #f44336;">' + (stats.stick_in_use || 0) + '</div>' +
                    '<div style="font-size: 12px; color: rgba(255,255,255,0.7);">使用中</div>' +
                    '</div>' +
                    '<div style="background: rgba(255,255,255,0.1); padding: 8px; border-radius: 6px; text-align: center;">' +
                    '<div style="font-size: 18px; font-weight: bold; color: #ff9800;">' + stickUsageRate + '%</div>' +
                    '<div style="font-size: 12px; color: rgba(255,255,255,0.7);">球杆使用率</div>' +
                    '</div>' +
                    '</div>';

                // 如果有联系信息，添加到内容中
                if (storeData.fullname || storeData.phone) {
                    content += '<div style="margin-top: 15px; padding-top: 10px; border-top: 1px solid rgba(255,255,255,0.2);">';
                    if (storeData.fullname) {
                        content += '<div style="color: rgba(255,255,255,0.8); font-size: 14px; margin-bottom: 5px;">' +
                            '<i class="fas fa-user"></i> 联系人：' + storeData.fullname +
                            '</div>';
                    }
                    if (storeData.phone) {
                        content += '<div style="color: rgba(255,255,255,0.8); font-size: 14px;">' +
                            '<i class="fas fa-phone"></i> 电话：' + storeData.phone +
                            '</div>';
                    }
                    content += '</div>';
                }

                content += '</div>';

                console.log('生成的信息窗口内容:', content);
                return content;
            },

            /**
             * 初始化排行榜功能
             */
            initRanking: function() {
                console.log('初始化排行榜功能');

                // 初始化排行榜状态
                Controller.api.rankingState = {
                    currentTab: 'area',    // 当前选中的标签：area, agent, store
                    areaType: 1,           // 区域类型：1=省，2=市，3=区
                    agentType: 1,          // 代理商类型：1=省代，2=市代，3=区代，4=业务员
                    orderType: 'order'     // 排序类型：order=订单量，amount=金额
                };

                // 修复下拉框样式
                $('.ranking-select').css({
                    'color': '#ffffff',
                    'background-color': 'rgba(255, 255, 255, 0.1)',
                    'border': '1px solid rgba(255, 255, 255, 0.2)',
                    'border-radius': '10px',
                    'padding': '8px 12px',
                    'padding-right': '30px',
                    'appearance': 'auto'
                });

                // 绑定排行榜标签切换事件
                $('.ranking-tab-btn').on('click', function() {
                    $('.ranking-tab-btn').removeClass('active');
                    $(this).addClass('active');

                    var type = $(this).data('type');
                    Controller.api.rankingState.currentTab = type;

                    // 根据标签类型显示或隐藏对应的选项
                    if (type === 'area') {
                        $('.area-options').show();
                        $('.agent-options').hide();
                    } else if (type === 'agent') {
                        $('.area-options').hide();
                        $('.agent-options').show();
                    } else {
                        $('.area-options').hide();
                        $('.agent-options').hide();
                    }

                    // 加载对应的排行榜数据
                    Controller.api.loadCurrentRankingData();
                });

                // 绑定区域类型选择事件
                $('#area-type-select').on('change', function() {
                    Controller.api.rankingState.areaType = parseInt($(this).val());
                    Controller.api.loadAreaRankingData();
                });

                // 绑定代理商类型选择事件
                $('#agent-type-select').on('change', function() {
                    Controller.api.rankingState.agentType = parseInt($(this).val());
                    Controller.api.loadAgentRankingData();
                });

                // 绑定排序类型按钮事件
                $('.ranking-sort-btn').on('click', function() {
                    $('.ranking-sort-btn').removeClass('active');
                    $(this).addClass('active');

                    Controller.api.rankingState.orderType = $(this).data('sort');
                    Controller.api.loadCurrentRankingData();
                });

                // 默认加载区域排行榜数据
                Controller.api.loadAreaRankingData();
            },

            /**
             * 加载当前排行榜数据
             */
            loadCurrentRankingData: function() {
                switch (Controller.api.rankingState.currentTab) {
                    case 'area':
                        Controller.api.loadAreaRankingData();
                        break;
                    case 'agent':
                        Controller.api.loadAgentRankingData();
                        break;
                    case 'store':
                        Controller.api.loadStoreRankingData();
                        break;
                }
            },

            /**
             * 加载区域排行榜数据
             */
            loadAreaRankingData: function() {
                console.log('加载区域排行榜数据');
                $('.ranking-loading').show();
                $('.ranking-list').empty();

                // 获取筛选参数
                var params = Controller.api.getFilterParams();
                
                // 添加排行榜特定参数
                params.area_type = Controller.api.rankingState.areaType;
                params.order_type = Controller.api.rankingState.orderType;
                params.limit = 10; // 显示前10名

                $.ajax({
                    url: 'mycurrency/tongji/getAreaOrderRanking',
                    type: 'GET',
                    dataType: 'json',
                    data: params,
                    success: function(response) {
                        $('.ranking-loading').hide();
                        
                        if (response.code === 1) {
                            Controller.api.renderRankingList(response.data);
                        } else {
                            Toastr.error(response.msg || '获取区域排行榜数据失败');
                        }
                    },
                    error: function() {
                        $('.ranking-loading').hide();
                        Toastr.error('获取区域排行榜数据失败，请稍后重试');
                    }
                });
            },

            /**
             * 加载代理商排行榜数据
             */
            loadAgentRankingData: function() {
                console.log('加载代理商排行榜数据');
                $('.ranking-loading').show();
                $('.ranking-list').empty();

                // 获取筛选参数
                var params = Controller.api.getFilterParams();
                
                // 添加排行榜特定参数
                params.agent_type = Controller.api.rankingState.agentType;
                params.order_type = Controller.api.rankingState.orderType;
                params.limit = 10; // 显示前10名

                $.ajax({
                    url: 'mycurrency/tongji/getAgentOrderRanking',
                    type: 'GET',
                    dataType: 'json',
                    data: params,
                    success: function(response) {
                        $('.ranking-loading').hide();
                        
                        if (response.code === 1) {
                            Controller.api.renderRankingList(response.data);
                        } else {
                            Toastr.error(response.msg || '获取代理商排行榜数据失败');
                        }
                    },
                    error: function() {
                        $('.ranking-loading').hide();
                        Toastr.error('获取代理商排行榜数据失败，请稍后重试');
                    }
                });
            },

            /**
             * 加载商家排行榜数据
             */
            loadStoreRankingData: function() {
                console.log('加载商家排行榜数据');
                $('.ranking-loading').show();
                $('.ranking-list').empty();

                // 获取筛选参数
                var params = Controller.api.getFilterParams();
                
                // 添加排行榜特定参数
                params.order_type = Controller.api.rankingState.orderType;
                params.limit = 10; // 显示前10名

                $.ajax({
                    url: 'mycurrency/tongji/getStoreOrderRanking',
                    type: 'GET',
                    dataType: 'json',
                    data: params,
                    success: function(response) {
                        $('.ranking-loading').hide();
                        
                        if (response.code === 1) {
                            Controller.api.renderRankingList(response.data);
                        } else {
                            Toastr.error(response.msg || '获取商家排行榜数据失败');
                        }
                    },
                    error: function() {
                        $('.ranking-loading').hide();
                        Toastr.error('获取商家排行榜数据失败，请稍后重试');
                    }
                });
            },

            /**
             * 渲染排行榜列表
             * @param {Array} data 排行榜数据
             */
            renderRankingList: function(data) {
                console.log('渲染排行榜数据:', data);
                var $list = $('.ranking-list');
                $list.empty();

                if (!data || data.length === 0) {
                    $list.html('<div class="text-center" style="padding: 50px; color: rgba(255,255,255,0.5);"><i class="fas fa-info-circle"></i> 暂无排行数据</div>');
                    return;
                }

                // 获取当前排序类型的显示文本
                var orderTypeText = Controller.api.rankingState.orderType === 'order' ? '订单量' : '金额';

                // 渲染每一项
                $.each(data, function(index, item) {
                    var rankClass = 'rank-other';
                    if (item.rank === 1) {
                        rankClass = 'rank-1';
                    } else if (item.rank === 2) {
                        rankClass = 'rank-2';
                    } else if (item.rank === 3) {
                        rankClass = 'rank-3';
                    }

                    // 获取显示名称
                    var displayName = '';
                    if (Controller.api.rankingState.currentTab === 'area') {
                        displayName = item.area_name || '';
                    } else if (Controller.api.rankingState.currentTab === 'agent') {
                        displayName = item.agent_name || item.name || '';
                    } else {
                        displayName = item.store_name || item.name || '';
                    }

                    // 获取显示值
                    var displayValue = item.value || '';
                    if (Controller.api.rankingState.orderType === 'amount') {
                        displayValue = '¥' + displayValue;
                    }

                    // 创建排行项
                    var $item = $('<div class="ranking-item">')
                        .append($('<div class="ranking-item-rank ' + rankClass + '">').text(item.rank))
                        .append(
                            $('<div class="ranking-item-info">')
                                .append($('<div class="ranking-item-name">').text(displayName))
                                .append(
                                    $('<div class="ranking-item-progress">')
                                        .append($('<div class="ranking-item-progress-bar">').css('width', item.percentage + '%'))
                                )
                        )
                        .append($('<div class="ranking-item-value">').text(displayValue));

                    $list.append($item);
                });
            },

            /**
             * 加载平台统计数据
             */
            loadPlatformStats: function() {
                // 获取筛选参数
                var filterData = Controller.api.getFilterParams();
                console.log('平台统计筛选参数:', filterData);

                // 发送AJAX请求获取平台统计数据
                $.ajax({
                    url: 'mycurrency/tongji/getPlatformStats',
                    type: 'GET',
                    dataType: 'json',
                    timeout: 10000, // 10秒超时
                    data: filterData,
                    success: function(response) {
                        console.log('接收到的平台统计响应数据:', response);

                        if (response.code === 1) {
                            console.log('准备更新平台统计数据:', response.data);
                            // 更新统计数据
                            Controller.api.updatePlatformStats(response.data);
                        } else {
                            console.error('获取平台统计数据失败:', response.msg);
                            Toastr.error(response.msg || '获取平台统计数据失败');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('获取平 台统计数据失败:', error);
                        console.error('HTTP状态:', status);
                        console.error('响应详情:', xhr.responseText);
                        Toastr.error('获取平台统计数据失败，请稍后重试');
                    }
                });
            },

            /**
             * 更新平台统计数据显示
             * @param {Object} data 统计数据
             */
            updatePlatformStats: function(data) {
                console.log('开始更新平台统计数据:', data);

                // 更新累计营业额
                $('#total-revenue').text(data.total_revenue ?? '0.00');
                
                // 更新今日营业额
                $('#today-revenue').text(data.today_revenue ?? '0.00');
                
                // 更新租用总时长
                $('#total-duration').text(data.total_duration ?? '0天0:00');
                
                // 更新今日租用时长
                $('#today-duration').text(data.today_duration ?? '0:00');
                
                // 更新租用总订单数
                $('#total-orders').text(data.total_orders ?? 0);
                
                // 更新今日订单数
                $('#today-orders').text(data.today_orders ?? 0);
                
                // 更新总代理数
                $('#total-agents').text(data.total_agents ?? 0);
                
                // 更新总商户数
                $('#total-stores').text(data.total_stores ?? 0);

                console.log('平台统计数据更新完成');
            }












        }
    };

    return Controller;
});


