
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-T">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>营业数据</title>
    <!-- 引入echarts图表库 -->
    <script src="/assets\third\node_modules\echarts\dist\echarts.min.js"></script>
    <!-- 引入laydate日期选择器 -->

    <style>
        #main{
            width: 100%!important;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
            margin: 0;
            background-color: #f0f2f5;
            padding-top: 10px;
            padding-bottom: 20px;
        }

        .main-content-area {
            width: 100%;
            background-color: #f0f2f5;
            padding: 10px 20px 20px 20px;
            border-radius: 8px;
        }

        .header-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            background-color: #ffffff;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .header-title-group h2 {
            margin: 0;
            font-size: 20px;
            color: #1a1a1a;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .header-title-group h2::before {
            content: "";
            display: inline-block;
            width: 4px;
            height: 20px;
            background: linear-gradient(to bottom, #1890ff, #0050b3);
            margin-right: 10px;
            border-radius: 2px;
        }

        .header-title-group p {
            margin: 5px 0 0 0;
            font-size: 13px;
            color: #666;
        }

        .date-filter-section {
            display: flex;
            align-items: center;
            font-size: 13px;
            color: #333;
            background-color: #f8f9fa;
            padding: 5px 10px;
            border-radius: 6px;
        }

        .date-filter-section .date-placeholder {
            color: #555;
            margin: 0 5px;
            padding: 6px 12px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            background-color: #ffffff;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        .date-filter-section .date-placeholder:hover {
            border-color: #1890ff;
        }

        .date-filter-section .date-separator {
            margin: 0 5px;
            color: #666;
        }

        .date-filter-section .calendar-icon-btn {
            width: 16px;
            height: 16px;
            margin-left: 8px;
            margin-right: 15px;
            cursor: pointer;
            vertical-align: middle;
            transition: transform 0.3s;
        }

        .date-filter-section .calendar-icon-btn:hover {
            transform: scale(1.1);
        }

        .filter-btn {
            padding: 6px 15px;
            border: 1px solid #d9d9d9;
            background-color: white;
            margin-left: 8px;
            cursor: pointer;
            border-radius: 4px;
            font-size: 13px;
            color: #333;
            transition: all 0.2s;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }

        .filter-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(24,144,255,0.15);
        }

        .filter-btn.active {
            background: linear-gradient(to right, #1890ff, #36a8ff);
            color: white;
            border-color: #1890ff;
            box-shadow: 0 2px 6px rgba(24,144,255,0.3);
        }

        .filter-btn.search-icon-btn {
            padding: 6px 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
        }

        .filter-btn.search-icon-btn:hover {
            background-color: #1890ff;
            border-color: #1890ff;
        }

        .filter-btn.search-icon-btn:hover img {
            filter: brightness(5);
        }

        .filter-btn.search-icon-btn img {
            width: 16px;
            height: 16px;
            transition: all 0.2s;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stats-grid-bottom {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background-color: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
            display: flex;
            flex-direction: column;
            transition: all 0.3s;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(230,230,230,0.7);
        }

        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.08);
            border-color: rgba(24,144,255,0.2);
        }

        .stat-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 6px;
            height: 100%;
            background: linear-gradient(to bottom, #1890ff, #36a8ff);
            border-radius: 3px 0 0 3px;
        }

        .stat-card.income::after {
            background: linear-gradient(to bottom, #52c41a, #73d13d); /* 收入类-绿色 */
        }

        .stat-card.expense::after {
            background: linear-gradient(to bottom, #fa8c16, #ffa940); /* 支出类-橙色 */
        }

        .stat-card.count::after {
            background: linear-gradient(to bottom, #722ed1, #9254de); /* 数量类-紫色 */
        }

        .stat-card.pending::after {
            background: linear-gradient(to bottom, #f5222d, #ff7875); /* 欠款类-红色 */
        }

        .stat-card .card-header-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .stat-card .icon {
            width: 32px;
            height: 32px;
            margin-right: 10px;
        }

        .stat-card .title {
            font-size: 14px;
            color: #333;
            font-weight: 600;
        }

        .stat-card.type-1 .title {
            font-size: 15px;
            color: #333;
        }

        .stat-card.type-2 .title {
            font-size: 15px;
            color: #333;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .stat-card .value {
            font-size: 28px;
            font-weight: 700;
            color: #1a1a1a;
            margin: 5px 0 8px 0;
            line-height: 1.2;
            display: flex;
            align-items: baseline;
        }

        .stat-card .value-trend {
            font-size: 13px;
            font-weight: normal;
            color: #52c41a;
            margin-left: 8px;
        }

        .stat-card .value-trend.down {
            color: #f5222d;
        }

        .stat-card .description {
            font-size: 12px;
            color: #666;
            background-color: #f9f9f9;
            padding: 8px 10px;
            border-radius: 4px;
            margin-top: auto;
            text-align: center;
            border-left: 3px solid #1890ff;
        }

        .stat-card.income .description {
            border-left-color: #52c41a;
        }

        .stat-card.expense .description {
            border-left-color: #fa8c16;
        }

        .stat-card.count .description {
            border-left-color: #722ed1;
        }

        .stat-card.pending .description {
            border-left-color: #f5222d;
        }

        .icon-placeholder {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            transition: all 0.3s;
        }

        .stat-card:hover .icon-placeholder {
            transform: scale(1.05);
        }

        .icon-graph {
            background-color: rgba(24, 144, 255, 0.1);
        }

        .icon-money {
            background-color: rgba(82, 196, 26, 0.1);
        }

        .icon-document {
            background-color: rgba(114, 46, 209, 0.1);
        }

        .icon-alert {
            background-color: rgba(245, 34, 45, 0.1);
        }

        .icon-graph img, .icon-document img, .icon-money img, .icon-alert img {
            width: 22px;
            height: 22px;
            transition: all 0.3s;
        }

        /* 图表区域样式 */
        .charts-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        .chart-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
            overflow: hidden;
            padding: 15px;
            border: 1px solid rgba(230,230,230,0.7);
        }

        .chart-title {
            font-size: 16px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
        }

        .chart-title::before {
            content: "";
            display: inline-block;
            width: 4px;
            height: 16px;
            background: linear-gradient(to bottom, #1890ff, #0050b3);
            margin-right: 8px;
            border-radius: 2px;
        }

        .chart-container {
            height: 300px;
            width: 100%;
        }

        /* 小型迷你图表 */
        .mini-chart {
            height: 40px;
            width: 80px;
            position: absolute;
            right: 15px;
            bottom: 15px;
            opacity: 0.7;
            transition: all 0.3s;
        }

        .stat-card:hover .mini-chart {
            opacity: 1;
        }

        /* 响应式调整 */
        @media (max-width: 1200px) {
            .charts-container {
                grid-template-columns: 1fr;
            }

            .stat-card .value {
                font-size: 24px;
            }
        }

        @media (max-width: 768px) {
            .header-section {
                flex-direction: column;
                align-items: flex-start;
            }

            .date-filter-section {
                margin-top: 15px;
                width: 100%;
                justify-content: space-between;
                flex-wrap: wrap;
            }

            .filter-btn {
                margin-top: 8px;
            }

            .stats-grid, .stats-grid-bottom {
                grid-template-columns: 1fr 1fr;
            }
        }

        @media (max-width: 576px) {
            .stats-grid, .stats-grid-bottom {
                grid-template-columns: 1fr;
            }
        }

        /* 加载状态样式 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255,255,255,0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            visibility: hidden;
            opacity: 0;
            transition: all 0.3s;
        }

        .loading-overlay.active {
            visibility: visible;
            opacity: 1;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f0f0f0;
            border-top: 3px solid #1890ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 添加数据加载提示样式 */
        .data-loading-tip {
            text-align: center;
            padding: 10px;
            color: #888;
            font-size: 14px;
            display: none;
        }

        .data-loading-tip.active {
            display: block;
        }
    </style>
</head>
<body>

<div class="loading-overlay">
    <div class="loading-spinner"></div>
</div>

<div class="main-content-area">
    <div class="header-section">
        <div class="header-title-group">
            <h2>营业数据</h2>
            <p>实现数据全面治理，提升数据价值!</p>
        </div>
        <div class="date-filter-section">
            <!-- <span class="date-placeholder">请选择开始时间</span>
            <span class="date-separator">—</span>
            <span class="date-placeholder">请选择结束时间</span> -->
            <img src="https://api.iconify.design/ant-design:calendar-outlined.svg?color=%23555555" alt="calendar" class="calendar-icon-btn">
            <button class="filter-btn" data-type="day">日</button>
            <button class="filter-btn" data-type="week">周</button>
            <button class="filter-btn" data-type="month">月</button>
            <button class="filter-btn search-icon-btn">
                <img src="https://api.iconify.design/ant-design:search-outlined.svg?color=%23555555" alt="search">
            </button>
        </div>
    </div>

    <div class="stats-grid">
        <!-- 营业收入卡片 -->
        <div class="stat-card type-1 income">
            <div class="card-header-item">
                <div class="icon-placeholder icon-money">
                    <img src="https://api.iconify.design/mdi:currency-cny.svg?color=%2352c41a" alt="Revenue Icon">
                </div>
                <span class="title">营业总收入(元)</span>
            </div>
            <p class="value">0.00</p>
            <div class="mini-chart" id="miniChart1"></div>
        </div>

        <div class="stat-card type-1 income">
            <div class="card-header-item">
                <div class="icon-placeholder icon-money">
                    <img src="https://api.iconify.design/mdi:currency-cny.svg?color=%2352c41a" alt="Revenue Icon">
                </div>
                <span class="title">昨日营业收入(元)</span>
            </div>
            <p class="value">0.00</p>
            <div class="mini-chart" id="miniChart2"></div>
        </div>

        <div class="stat-card type-1 income">
            <div class="card-header-item">
                <div class="icon-placeholder icon-money">
                    <img src="https://api.iconify.design/mdi:cash-multiple.svg?color=%2352c41a" alt="Payment Icon">
                </div>
                <span class="title">昨日实收金额(元)</span>
            </div>
            <p class="value">0.00</p>
            <div class="mini-chart" id="miniChart3
"></div>
        </div>

        <div class="stat-card type-1 count">
            <div class="card-header-item">
                <div class="icon-placeholder icon-document">
                    <img src="https://api.iconify.design/mdi:file-document-outline.svg?color=%23722ed1" alt="Document Icon">
                </div>
                <span class="title">订单总笔数(笔)</span>
            </div>
            <p class="value">0</p>
            <div class="mini-chart" id="miniChart4"></div>
        </div>

        <div class="stat-card type-1 count">
            <div class="card-header-item">
                <div class="icon-placeholder icon-document">
                    <img src="https://api.iconify.design/mdi:file-document-outline.svg?color=%23722ed1" alt="Document Icon">
                </div>
                <span class="title">昨日支付笔数(笔)</span>
            </div>
            <p class="value">0</p>
            <div class="mini-chart" id="miniChart5"></div>
        </div>
    </div>

    <div class="stats-grid-bottom">
        <!-- 欠款相关卡片 -->
        <div class="stat-card type-2 pending">
            <span class="title">未付款/欠款总额(元)</span>
            <p class="value">0.00</p>
            <p class="description">昨日新增欠款(元):0.00</p>
            <div class="mini-chart" id="miniChart6"></div>
        </div>

        <div class="stat-card type-2 pending">
            <span class="title">历史欠款订单总笔数(笔)</span>
            <p class="value">0</p>
            <p class="description">昨日新增欠款订单(笔):0</p>
            <div class="mini-chart" id="miniChart7"></div>
        </div>
    </div>

    <!-- 图表区域 -->
<!--     <div class="charts-container">
        <div class="chart-card">
            <div class="chart-title">营业收入趋势图</div>
            <div class="data-loading-tip">正在加载数据，请稍候...</div>
            <div class="chart-container" id="revenueChart"></div>
        </div>

        <div class="chart-card">
            <div class="chart-title">订单与欠款统计</div>
            <div class="data-loading-tip">正在加载数据，请稍候...</div>
            <div class="chart-container" id="ordersChart"></div>
        </div>
    </div> -->
</div>

</body>
</html>